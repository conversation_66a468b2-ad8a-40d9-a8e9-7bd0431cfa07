export const SOCKET_EVENTS = {
  // Connection events
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  CONNECT_ERROR: 'connect_error',
  
  // Notification events
  NOTIFICATION: 'new_notification',
  FORUM_MESSAGE: 'message_received',
  GAME_UPDATE: 'game_update',
  
  // User presence events
  USER_PRESENCE: 'user_online',
  UPDATE_PRESENCE: 'status_update',
  
  // Forum events
  JOIN_GROUP: 'join_group',
  LEAVE_GROUP: 'leave_group',
  TYPING_INDICATOR: 'typing_start',
  MESSAGE_DELIVERED: 'new_message',
  MARK_READ: 'mark_dm_read',
  MESSAGE_REACTION: 'message_reaction',
  GROUP_UPDATE: 'group_update',
  
  // Game events
  JOIN_GAME: 'join_game',
  LEAVE_GAME: 'leave_game',
  GAME_ACTION: 'player_answered',
  
  // Booking events
  PACKAGE_BOOKING: 'new_package_booking',
  BOOKING_UPDATE: 'booking_update',
  
  // Cafeteria events
  ORDER_CREATED: 'order_created',
  ORDER_UPDATE: 'order_update',
  SPECIAL_ORDER_UPDATE: 'special_order_update',
  
  // Transaction events
  TRANSACTION_UPDATE: 'transaction_update',
  WALLET_UPDATE: 'wallet_update',
  
  // Reward events
  REWARD_RECEIVED: 'reward_received',
  
  // System events
  SYSTEM_NOTIFICATION: 'system_notification',
  MAINTENANCE_NOTIFICATION: 'maintenance_notification',
  EMERGENCY_NOTIFICATION: 'emergency_notification',
  
  // Referral events
  NEW_REFERRAL_CREATED: 'new_referral_created',
  REFERRAL_STATUS_UPDATED: 'referral_status_updated',
  REFERRAL_COMMENT_ADDED: 'referral_comment_added',
  
  // Staff events
  NEW_STAFF_CREATED: 'new_staff_created',
  PROFILE_UPDATED: 'profile_updated',
  LOGIN_SUCCESSFUL: 'login_successful',
  
  // Direct messaging events
  DM_USER_TYPING: 'dm_user_typing',
  DM_USER_STOPPED_TYPING: 'dm_user_stopped_typing',
  DIRECT_MESSAGE_RECEIVED: 'direct_message_received',
  DM_MESSAGE_READ: 'dm_message_read',
  
  // Channel events
  USER_JOINED_GROUP: 'user_joined_group',
  USER_LEFT_GROUP: 'user_left_group',
  USER_TYPING: 'user_typing',
  USER_STOPPED_TYPING: 'user_stopped_typing',
  MENTIONED_IN_MESSAGE: 'mentioned_in_message',
  MESSAGE_UPDATED: 'message_updated',
  MESSAGE_DELETED: 'message_deleted',
  MESSAGES_READ: 'messages_read',
  
  // Game events (additional)
  PLAYER_JOINED: 'player_joined',
  PLAYER_LEFT: 'player_left',
  GAME_STARTED: 'game_started',
  GAME_COMPLETED: 'game_completed',
  LEADERBOARD_UPDATED: 'leaderboard_updated',
  
  // Presence events
  USER_OFFLINE: 'user_offline',
  USER_STATUS_CHANGED: 'user_status_changed',
  
  // Test events
  TEST_NOTIFICATION: 'test_notification',
} as const;

export const SOCKET_CONFIG = {
  transports: ['websocket', 'polling'] as string[],
  autoConnect: true,
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
  timeout: 20000,
} as const;