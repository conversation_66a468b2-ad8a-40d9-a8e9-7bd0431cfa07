'use client';

import React, { useEffect } from 'react';
import socketService from '@/services/websocket';
import { forumStore } from '@/store/forumStore';
import { SOCKET_EVENTS } from '@/lib/socket-config';

export const WebSocketProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  useEffect(() => {
    forumStore.setConnectionStatus('connecting');
    socketService.connect();

    const handleConnected = () => {
      forumStore.setConnectionStatus('connected');
      socketService.updatePresence('online');
    };

    const handleDisconnected = () => {
      forumStore.setConnectionStatus('disconnected');
    };

    socketService.on(SOCKET_EVENTS.CONNECT, handleConnected);
    socketService.on(SOCKET_EVENTS.DISCONNECT, handleDisconnected);

    const handleVisibilityChange = () => {
      if (document.hidden) {
        socketService.updatePresence('away');
      } else {
        socketService.updatePresence('online');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      socketService.off(SOCKET_EVENTS.CONNECT, handleConnected);
      socketService.off(SOCKET_EVENTS.DISCONNECT, handleDisconnected);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      socketService.updatePresence('offline');
      socketService.disconnect();
    };
  }, []);

  return <>{children}</>;
};

export default WebSocketProvider;
